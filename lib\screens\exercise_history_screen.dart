import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../providers/app_state.dart';
import '../theme/app_theme.dart';
import '../models/exercise_history.dart';
import '../utils/persian_utils.dart';

class ExerciseHistoryScreen extends StatefulWidget {
  const ExerciseHistoryScreen({super.key});

  @override
  State<ExerciseHistoryScreen> createState() => _ExerciseHistoryScreenState();
}

class _ExerciseHistoryScreenState extends State<ExerciseHistoryScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(
              FeatherIcons.arrowLeft,
              color: Color(0xFF2F4F4F),
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ],
        title: Text(
          'تاریخچه کامل تمرینات',
          style: const TextStyle(
            fontFamily: 'Samim',
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2F4F4F),
          ),
        ),
        centerTitle: true,
      ),
      body: Consumer<AppState>(
        builder: (context, appState, child) {
          return FutureBuilder<List<ExerciseHistory>>(
            future: appState.getAllExerciseHistory(),
            builder: (context, snapshot) {
              // Show loading indicator while waiting for data
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: CircularProgressIndicator(
                      color: AppTheme.primaryColor,
                    ),
                  ),
                );
              }

              // Show error message if there's an error
              if (snapshot.hasError) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          FeatherIcons.alertCircle,
                          size: 48,
                          color: Colors.red,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading exercise history',
                          style: GoogleFonts.lato(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Please try again later',
                          style: GoogleFonts.lato(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              // Get the history data
              final history = snapshot.data ?? [];

              // Show empty state if no history
              if (history.isEmpty) {
                return _buildEmptyState(context);
              }

              // Group history by date
              final groupedHistory = _groupHistoryByDate(history);

              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: groupedHistory.length,
                itemBuilder: (context, index) {
                  final dateGroup = groupedHistory[index];
                  return _buildDateGroup(context, dateGroup);
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FeatherIcons.activity,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 24),
            Text(
              'هنوز تمرینی ثبت نشده است',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontFamily: 'Samim',
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2F4F4F),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'با انجام اولین تمرین تنفسی خود، سفر سلامتی را آغاز کنید',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontFamily: 'Samim',
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: const Icon(FeatherIcons.play, size: 18),
              label: Text(
                'شروع تمرین تنفسی',
                style: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _groupHistoryByDate(
      List<ExerciseHistory> history) {
    final Map<String, List<ExerciseHistory>> grouped = {};
    final now = DateTime.now();

    for (final exercise in history) {
      final exerciseDate = exercise.date;
      final difference = now.difference(exerciseDate).inDays;

      String dateKey;
      if (difference == 0) {
        dateKey = 'امروز';
      } else if (difference == 1) {
        dateKey = 'دیروز';
      } else {
        dateKey = PersianUtils.toPersianNumbers(
            DateFormat('EEEE, MMM d, yyyy', 'fa').format(exerciseDate));
      }

      if (!grouped.containsKey(dateKey)) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(exercise);
    }

    // Convert to list and maintain order
    return grouped.entries
        .map((entry) => {
              'date': entry.key,
              'exercises': entry.value,
            })
        .toList();
  }

  Widget _buildDateGroup(BuildContext context, Map<String, dynamic> dateGroup) {
    final String date = dateGroup['date'];
    final List<ExerciseHistory> exercises = dateGroup['exercises'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            date,
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2F4F4F),
            ),
          ),
        ),
        ...exercises.map((exercise) => _buildHistoryItem(
              context,
              exercise.technique,
              exercise.date,
              exercise.color,
              exercise.iconName,
            )),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildHistoryItem(
    BuildContext context,
    String technique,
    DateTime date,
    Color color,
    String iconName,
  ) {
    final formattedTime =
        PersianUtils.toPersianNumbers(DateFormat('h:mm a', 'fa').format(date));

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(51), // 0.2 * 255 ≈ 51
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getIconData(iconName),
            color: color,
            size: 24,
          ),
        ),
        title: Text(
          technique,
          style: GoogleFonts.lato(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          formattedTime,
          style: GoogleFonts.lato(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        trailing: const Icon(
          FeatherIcons.chevronLeft,
          size: 18,
          color: Colors.grey,
        ),
        onTap: () {
          // Show exercise details
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'شما تمرین «$technique» را در ساعت $formattedTime تکمیل کردید',
                style: const TextStyle(fontFamily: 'Samim'),
              ),
              duration: const Duration(seconds: 2),
            ),
          );
        },
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'wind':
        return FeatherIcons.wind;
      case 'sun':
        return FeatherIcons.sun;
      case 'moon':
        return FeatherIcons.moon;
      case 'heart':
        return FeatherIcons.heart;
      case 'zap':
        return FeatherIcons.zap;
      case 'target':
        return FeatherIcons.target;
      default:
        return FeatherIcons.circle;
    }
  }
}
